/**
 * 项目分享页面相关的TypeScript类型定义
 * 基于job-requiremnet-detail.vue中的数据结构
 */

// 基础用户信息
export interface UserDetail {
  userName: string;
  formalPhotoUrl?: string;
  wechatQrCodeUrl?: string;
}

// 项目属性
export interface ProjectProperty {
  key: string;
  value: string | number;
  valueName?: string;
}

// 职位需求信息
export interface JobRequirement {
  id: number;
  status: number;
  priority: number;
  positionId: number;
  position: any;
  customer: any;
  properties: ProjectProperty[];
  processName: string;
  customerId: number;
  customerName: string;
  positionStartDate: string;
  companyId: number;
  canPublishToPlatform: boolean;
  pmUserDetail: UserDetail[];
  isNewProject?: number | null;
}

// 职位详情信息
export interface PositionDetail {
  id: number;
  positionTitle: string;
  quantityRequired: number;
  areaStr: string;
  salaryFrom?: number;
  salaryTo?: number;
  salaryUnit?: number;
  isManager?: boolean;
  tags: Array<{ name: string; id?: number }>;
  positionDescription?: string;
  positionRequirement?: string;
  workContent?: string;
  interviewProcess?: string;
}

// 客户详情信息
export interface CustomerDetail {
  id: number;
  customerFullName: string;
  customerIntroduction?: string;
  areaStr?: string;
  isNewCustomer?: boolean;
  hasPaymentHistory?: boolean;
  contactors?: Array<{
    name: string;
    phone: string;
    wechatNumber: string;
  }>;
}

// 财务配置信息
export interface FinanceConfig {
  paymentTimes: number;
  paymentCycles: Array<{
    name: string;
    desc: string;
  }>;
  highestCommission: number;
  steps: Array<{
    name: string;
    desc: string;
  }>;
  salaryUnit: number;
  salaryUnitStr: string;
}

// 分享数据
export interface ShareData {
  content: string;
}

// 页面状态
export interface PageStatus {
  positionDetailLoading: boolean;
  jobRequirementLoading: boolean;
  positionLoading: boolean;
  customerLoading: boolean;
  isCurrentCompanyJob: boolean;
  hasShareJobPermission: boolean;
  sharePopoverVisible: boolean;
}

// 项目详情完整数据结构
export interface ProjectDetailData {
  jobRequirement: JobRequirement;
  positionDetail: PositionDetail;
  customerDetail: CustomerDetail;
  financeConfig: FinanceConfig;
  shareData: ShareData;
  status: PageStatus;
}

// API响应数据结构
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 标签类型
export interface TagItem {
  id?: number;
  name: string;
  color?: string;
  type?: 'primary' | 'success' | 'warning' | 'error' | 'info';
}

// 内容区域类型
export interface ContentSection {
  title: string;
  content: string;
  icon?: React.ReactNode;
}

// 按钮配置
export interface ActionButton {
  text: string;
  type?: 'primary' | 'default';
  onClick?: () => void;
  loading?: boolean;
  disabled?: boolean;
}
