# ProjectsShare 组件

项目分享页面组件，实现响应式设计，完全匹配提供的截图样式。

## 功能特性

- ✅ 完全响应式设计，适配PC端和移动端
- ✅ TypeScript类型安全
- ✅ 基于Vue参考文件的数据结构
- ✅ PC端按钮在底部文档流，移动端固定定位
- ✅ 使用现有API获取数据
- ✅ 完整的错误处理和加载状态

## 组件结构

```
ProjectsShare/
├── index.tsx                 # 主组件
├── types.ts                  # TypeScript类型定义
├── styles.module.scss        # 样式文件
├── README.md                 # 说明文档
├── components/               # 子组件
│   ├── index.ts             # 组件导出
│   ├── Layout.tsx           # 布局组件
│   ├── ActionButtons.tsx    # 按钮组件
│   ├── ProjectHeader.tsx    # 项目头部
│   └── ProjectContent.tsx   # 项目内容
└── hooks/                   # 自定义Hook
    └── useProjectDetail.ts  # 数据获取Hook
```

## 使用方法

### 基本使用

```tsx
import ProjectsShare from '@/pages/rcn/components/ProjectsShare';

// 在路由中使用
<Route path="/projects/:jobRequirementId/share" component={ProjectsShare} />
```

### 数据结构

组件使用以下主要数据类型：

- `JobRequirement`: 职位需求信息
- `PositionDetail`: 职位详情信息  
- `CustomerDetail`: 客户详情信息
- `FinanceConfig`: 财务配置信息

### API接口

使用 `api.rcn.project.rcnGetProjectDetail(jobRequirementId)` 获取项目详情数据。

## 响应式设计

### 断点配置

- `xs`: 0px
- `sm`: 640px
- `md`: 768px  
- `lg`: 1024px
- `xl`: 1280px
- `2xl`: 1536px

### 布局适配

**PC端 (≥768px)**:
- 容器最大宽度1200px，居中显示
- 信息网格4列布局
- 按钮组在底部文档流中
- 较大的字体和间距

**移动端 (<768px)**:
- 全宽布局，左右16px边距
- 信息网格单列布局
- 按钮组固定在屏幕底部
- 适配安全区域
- 较小的字体和间距

## 样式系统

### 主题色

- 主色: `#FE9111` (橙色)
- 悬停色: `#E8820F`

### 标签颜色

- 蓝色: `blue` - 普通信息
- 绿色: `green` - 新岗位
- 橙色: `orange` - 高优先级  
- 红色: `red` - 急招
- 紫色: `purple` - 管理岗
- 灰色: `gray` - 默认

### 卡片样式

- 白色背景
- 8px圆角
- 浅灰色边框
- 轻微阴影

## 组件API

### ProjectsShare

主组件，自动从URL参数获取`jobRequirementId`。

### ProjectHeader

项目头部信息组件。

**Props:**
- `jobRequirement`: 职位需求数据
- `positionDetail`: 职位详情数据
- `customerDetail`: 客户详情数据

### ProjectContent

项目内容区域组件。

**Props:**
- `positionDetail`: 职位详情数据
- `customerDetail`: 客户详情数据
- `financeConfig`: 财务配置数据

### ActionButtons

响应式按钮组组件。

**Props:**
- `buttons`: 按钮配置数组

## 自定义Hook

### useProjectDetail

数据获取和状态管理Hook。

**参数:**
- `jobRequirementId`: 项目ID

**返回值:**
- `data`: 项目详情数据
- `loading`: 加载状态
- `error`: 错误信息
- `refresh`: 刷新函数

## 开发注意事项

1. 确保已安装所需依赖：`antd`, `lucide-react`, `clsx`
2. 确保响应式工具已正确配置
3. 测试不同屏幕尺寸下的显示效果
4. 验证API数据结构与类型定义匹配

## 浏览器兼容性

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
