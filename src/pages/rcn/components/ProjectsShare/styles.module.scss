/**
 * 项目分享页面样式
 * 确保与截图样式匹配
 */

.projectShare {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  // 移动端安全区域适配
  @media (max-width: 768px) {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  
  @media (max-width: 768px) {
    padding: 16px;
    max-width: 100%;
  }
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.cardPadding {
  padding: 24px;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

// 项目头部样式
.projectHeader {
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 8px;
    line-height: 1.3;
    
    @media (max-width: 768px) {
      font-size: 20px;
    }
  }
  
  .companyInfo {
    color: #666;
    font-size: 14px;
    margin-bottom: 16px;
    
    .company {
      font-weight: 500;
      color: #333;
    }
    
    .pm {
      margin-left: 12px;
      
      @media (max-width: 768px) {
        margin-left: 0;
        margin-top: 4px;
        display: block;
      }
    }
  }
  
  .infoGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }
  
  .infoItem {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .icon {
      color: #999;
      flex-shrink: 0;
    }
    
    .content {
      .label {
        font-size: 12px;
        color: #999;
        display: block;
        margin-bottom: 2px;
      }
      
      .value {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

// 标签样式
.tagGroup {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  
  @media (max-width: 768px) {
    gap: 6px;
  }
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  
  @media (max-width: 768px) {
    padding: 3px 10px;
    font-size: 11px;
  }
  
  // 不同颜色的标签
  &.blue {
    background-color: #e6f7ff;
    color: #1890ff;
    border-color: #91d5ff;
  }
  
  &.green {
    background-color: #f6ffed;
    color: #52c41a;
    border-color: #b7eb8f;
  }
  
  &.orange {
    background-color: #fff7e6;
    color: #fa8c16;
    border-color: #ffd591;
  }
  
  &.red {
    background-color: #fff2f0;
    color: #ff4d4f;
    border-color: #ffb3b3;
  }
  
  &.purple {
    background-color: #f9f0ff;
    color: #722ed1;
    border-color: #d3adf7;
  }
  
  &.gray {
    background-color: #fafafa;
    color: #666;
    border-color: #d9d9d9;
  }
}

// 内容区域样式
.contentSection {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .sectionTitle {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 12px;
    
    @media (max-width: 768px) {
      font-size: 15px;
    }
    
    .icon {
      color: #666;
    }
  }
  
  .sectionContent {
    color: #595959;
    line-height: 1.6;
    font-size: 14px;
    
    @media (max-width: 768px) {
      font-size: 13px;
    }
    
    // 处理HTML内容样式
    :global {
      p {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      ul, ol {
        padding-left: 20px;
        margin-bottom: 8px;
      }
      
      li {
        margin-bottom: 4px;
      }
      
      strong {
        font-weight: 600;
        color: #262626;
      }
    }
  }
}

// 佣金信息特殊样式
.commissionInfo {
  .commissionItem {
    background-color: #fafafa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.5;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .highlight {
    background-color: #e6f7ff;
    color: #1890ff;
    padding: 12px;
    border-radius: 6px;
    font-size: 13px;
    
    .label {
      font-weight: 600;
    }
  }
}

// 按钮样式
.actionButtons {
  // PC端样式
  &.desktop {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
  }
  
  // 移动端样式
  &.mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border-top: 1px solid #f0f0f0;
    padding: 12px 16px;
    padding-bottom: calc(12px + env(safe-area-inset-bottom));
    
    .buttonGroup {
      display: flex;
      gap: 12px;
    }
  }
  
  // 占位元素（防止移动端内容被遮挡）
  &.spacer {
    height: 80px;
  }
}

.actionButton {
  min-width: 120px;
  height: 48px;
  font-weight: 500;
  border-radius: 6px;
  
  @media (max-width: 768px) {
    flex: 1;
    min-width: auto;
  }
  
  &.primary {
    background-color: #fe9111;
    border-color: #fe9111;
    
    &:hover {
      background-color: #e8820f;
      border-color: #e8820f;
    }
    
    &:focus {
      background-color: #e8820f;
      border-color: #e8820f;
    }
  }
}

// 加载和错误状态样式
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  
  .loadingContent {
    text-align: center;
    
    .loadingText {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }
}

.errorContainer {
  margin: 40px 0;
  
  .retryButton {
    color: #1890ff;
    text-decoration: underline;
    background: none;
    border: none;
    cursor: pointer;
    
    &:hover {
      color: #40a9ff;
    }
  }
}
