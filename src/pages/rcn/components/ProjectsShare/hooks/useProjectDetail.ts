/**
 * 项目详情数据获取和状态管理Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import api from '@/services';
import { 
  ProjectDetailData, 
  JobRequirement, 
  PositionDetail, 
  CustomerDetail, 
  FinanceConfig,
  PageStatus 
} from '../types';

interface UseProjectDetailProps {
  jobRequirementId: number | string;
}

interface UseProjectDetailReturn {
  data: ProjectDetailData | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export const useProjectDetail = ({ 
  jobRequirementId 
}: UseProjectDetailProps): UseProjectDetailReturn => {
  const [data, setData] = useState<ProjectDetailData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 初始化状态
  const initializeData = useCallback((): ProjectDetailData => ({
    jobRequirement: {
      id: 0,
      status: 0,
      priority: 0,
      positionId: 0,
      position: {},
      customer: {},
      properties: [],
      processName: '',
      customerId: 0,
      customerName: '',
      positionStartDate: '',
      companyId: 0,
      canPublishToPlatform: false,
      pmUserDetail: [],
      isNewProject: null,
    },
    positionDetail: {
      id: 0,
      positionTitle: '',
      quantityRequired: 0,
      areaStr: '',
      tags: [],
    },
    customerDetail: {
      id: 0,
      customerFullName: '',
    },
    financeConfig: {
      paymentTimes: 0,
      paymentCycles: [],
      highestCommission: 0,
      steps: [],
      salaryUnit: 0,
      salaryUnitStr: '',
    },
    shareData: {
      content: '',
    },
    status: {
      positionDetailLoading: false,
      jobRequirementLoading: false,
      positionLoading: false,
      customerLoading: false,
      isCurrentCompanyJob: false,
      hasShareJobPermission: false,
      sharePopoverVisible: false,
    },
  }), []);

  // 格式化薪资显示
  const formatSalary = (position: PositionDetail): string => {
    if (!position.salaryFrom && !position.salaryTo) {
      return '薪资面议';
    }
    
    if (position.salaryFrom && position.salaryTo) {
      return `${position.salaryFrom}k-${position.salaryTo}k`;
    }
    
    if (position.salaryFrom) {
      return `${position.salaryFrom}k+`;
    }
    
    if (position.salaryTo) {
      return `≤${position.salaryTo}k`;
    }
    
    return '薪资面议';
  };

  // 获取用户信息（BD、PM、CA）
  const getUsers = (properties: any[], type: 'bd' | 'pm' | 'ca'): string => {
    const users: string[] = [];
    properties.forEach((item: any) => {
      if (item.key?.toLowerCase() === type) {
        users.push(item.valueName || item.value);
      }
    });
    return users.join(' / ') || '-';
  };

  // 处理API响应数据
  const processApiData = useCallback((apiResponse: any): ProjectDetailData => {
    const initialData = initializeData();
    
    if (!apiResponse) {
      return initialData;
    }

    // 处理职位需求数据
    const jobRequirement: JobRequirement = {
      ...initialData.jobRequirement,
      ...apiResponse,
      pmUserDetail: apiResponse.pmUserDetail || [],
    };

    // 处理职位详情数据
    const positionDetail: PositionDetail = {
      ...initialData.positionDetail,
      ...apiResponse.position,
      tags: apiResponse.position?.tags || [],
    };

    // 处理客户详情数据
    const customerDetail: CustomerDetail = {
      ...initialData.customerDetail,
      ...apiResponse.customer,
    };

    // 处理财务配置数据
    const financeConfig: FinanceConfig = {
      ...initialData.financeConfig,
      ...apiResponse.financeConfig,
    };

    return {
      jobRequirement,
      positionDetail,
      customerDetail,
      financeConfig,
      shareData: initialData.shareData,
      status: initialData.status,
    };
  }, [initializeData]);

  // 获取项目详情数据
  const fetchProjectDetail = useCallback(async () => {
    if (!jobRequirementId) {
      setError('缺少项目ID参数');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await api.rcn.project.rcnGetProjectDetail(jobRequirementId);
      
      if (response) {
        const processedData = processApiData(response);
        setData(processedData);
      } else {
        throw new Error('获取项目详情失败');
      }
    } catch (err: any) {
      const errorMessage = err.message || '获取项目详情失败，请稍后重试';
      setError(errorMessage);
      message.error(errorMessage);
      console.error('Failed to fetch project details:', err);
    } finally {
      setLoading(false);
    }
  }, [jobRequirementId, processApiData]);

  // 刷新数据
  const refresh = useCallback(async () => {
    await fetchProjectDetail();
  }, [fetchProjectDetail]);

  // 初始化数据获取
  useEffect(() => {
    fetchProjectDetail();
  }, [fetchProjectDetail]);

  return {
    data,
    loading,
    error,
    refresh,
  };
};
