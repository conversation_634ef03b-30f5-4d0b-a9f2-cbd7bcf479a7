/**
 * 项目内容区域组件
 * 包含职位描述、工作内容、面试流程、人才要求等
 */

import React from 'react';
import { 
  FileText, 
  Briefcase, 
  Users, 
  CheckCircle, 
  Building,
  DollarSign 
} from 'lucide-react';
import clsx from 'clsx';
import { useBreakpoint } from '../../theme/responsive';
import { Card, ContentSection, Divider } from './Layout';
import { PositionDetail, CustomerDetail, FinanceConfig } from '../types';

interface ProjectContentProps {
  positionDetail: PositionDetail;
  customerDetail: CustomerDetail;
  financeConfig: FinanceConfig;
  className?: string;
}

export const ProjectContent: React.FC<ProjectContentProps> = ({
  positionDetail,
  customerDetail,
  financeConfig,
  className
}) => {
  const { isMobile } = useBreakpoint();

  // 格式化文本内容
  const formatContent = (content?: string): string => {
    if (!content) return '暂无相关信息';
    return content.replace(/\n/g, '<br />');
  };

  // 渲染HTML内容
  const renderHtmlContent = (content: string) => (
    <div 
      className="prose prose-sm max-w-none"
      dangerouslySetInnerHTML={{ __html: formatContent(content) }}
    />
  );

  return (
    <div className={clsx('space-y-6', className)}>
      {/* 职位描述 */}
      <Card>
        <ContentSection
          title="职位描述"
          icon={<FileText className="w-5 h-5" />}
        >
          {positionDetail.workDetail ? (
            renderHtmlContent(positionDetail.workDetail)
          ) : (
            <p className="text-gray-500">暂无职位描述</p>
          )}
        </ContentSection>
      </Card>

      {/* 工作内容 */}

      {/* 面试流程 */}
      {positionDetail.interviewProcess && (
        <Card>
          <ContentSection
            title="面试流程"
            icon={<CheckCircle className="w-5 h-5" />}
          >
            {renderHtmlContent(positionDetail.interviewProcess)}
          </ContentSection>
        </Card>
      )}

      {/* 人才要求 */}
      <Card>
        <ContentSection
          title="人才要求"
          icon={<Users className="w-5 h-5" />}
        >
          {positionDetail.positionRequirement ? (
            renderHtmlContent(positionDetail.positionRequirement)
          ) : (
            <p className="text-gray-500">暂无具体要求</p>
          )}
        </ContentSection>
      </Card>

      {/* 企业信息 */}
      <Card>
        <ContentSection
          title="企业信息"
          icon={<Building className="w-5 h-5" />}
        >
          <div className="space-y-3">
            <div>
              <h5 className="font-semibold text-gray-900 mb-2">
                {customerDetail.customerFullName || '企业名称'}
              </h5>
              {customerDetail.areaStr && (
                <p className="text-sm text-gray-600 mb-2">
                  <span className="font-medium">所在地区：</span>
                  {customerDetail.areaStr}
                </p>
              )}
            </div>
            
            {customerDetail.customerIntroduction ? (
              <div>
                <h5 className="font-medium text-gray-800 mb-2">企业介绍</h5>
                {renderHtmlContent(customerDetail.customerIntroduction)}
              </div>
            ) : (
              <p className="text-gray-500">暂无企业介绍</p>
            )}
          </div>
        </ContentSection>
      </Card>

      {/* 佣金信息 */}
      {financeConfig.paymentTimes > 0 && (
        <Card>
          <ContentSection
            title="接单预计佣金说明"
            icon={<DollarSign className="w-5 h-5" />}
          >
            <div className="space-y-4">
              {/* 佣金规则 */}
              {financeConfig.steps.length > 0 && (
                <div>
                  <h6 className="font-medium text-gray-800 mb-2">佣金规则：</h6>
                  <div className="space-y-2">
                    {financeConfig.steps.map((step, index) => (
                      <div 
                        key={index}
                        className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md"
                        dangerouslySetInnerHTML={{ __html: step.desc }}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* 支付分期 */}
              <div>
                <h6 className="font-medium text-gray-800 mb-2">
                  支付分期方式：共 {financeConfig.paymentTimes} 期
                </h6>
              </div>

              {/* 支付周期 */}
              {financeConfig.paymentCycles.length > 0 && (
                <div>
                  <h6 className="font-medium text-gray-800 mb-2">客户支付周期：</h6>
                  <div className="space-y-2">
                    {financeConfig.paymentCycles.map((cycle, index) => (
                      <div 
                        key={index}
                        className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md"
                        dangerouslySetInnerHTML={{ __html: cycle.desc }}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* SmartDeer支付周期 */}
              <div className="bg-blue-50 p-3 rounded-md">
                <p className="text-sm text-blue-800">
                  <span className="font-medium">SmartDeer 支付周期：</span>
                  客户付款后 10 个工作日内
                </p>
              </div>
            </div>
          </ContentSection>
        </Card>
      )}
    </div>
  );
};
