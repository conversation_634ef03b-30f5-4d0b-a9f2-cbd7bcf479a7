/**
 * 项目头部信息组件
 * 包含职位标题、公司信息、PM信息等
 */

import React from 'react';
import { User, MapPin, Calendar, DollarSign, Users } from 'lucide-react';
import clsx from 'clsx';
import { useBreakpoint } from '../../theme/responsive';
import { Card, TagGroup } from './Layout';
import { JobRequirement, PositionDetail, CustomerDetail } from '../types';

interface ProjectHeaderProps {
  jobRequirement: JobRequirement;
  positionDetail: PositionDetail;
  customerDetail: CustomerDetail;
  className?: string;
}

enum SALARY_CALC_UNIT {
  DAY = 1,
  MONTH = 0,
  YEAR = 2
}

enum SALARY_UNIT {
  CNY = 0,
  USD = 1
}

type Salary = {
  // 薪资描述信息
  salaryTo: number
  salaryFrom: number
  salaryTime: number
  salaryTimeUnit: SALARY_CALC_UNIT
  salaryUnit: SALARY_UNIT
}

const SALARY_UNIT_DICT = [
  {
    "name": "人民币",
    "id": 0,
    "key": "RMB"
  },
  {
    "name": "美元",
    "id": 1,
    "key": "USD"
  },
  {
    "name": "USDT",
    "id": 2,
    "key": "USDT"
  },
  {
    "name": "USDC",
    "id": 3,
    "key": "USDC"
  },
  {
    "name": "欧元",
    "id": 4,
    "key": "EUR"
  },
  {
    "name": "新加坡元",
    "id": 5,
    "key": "SGD"
  },
  {
    "name": "港币",
    "id": 6,
    "key": "HKD"
  },
  {
    "name": "英镑",
    "id": 7,
    "key": "GBP"
  },
  {
    "name": "印度卢比",
    "id": 8,
    "key": "INR"
  },
  {
    "name": "澳大利亚币",
    "id": 9,
    "key": "AUD"
  },
  {
    "name": "马来西亚林吉特",
    "id": 10,
    "key": "MYR"
  },
  {
    "name": "日元",
    "id": 11,
    "key": "JPY"
  },
  {
    "name": "俄罗斯卢布",
    "id": 12,
    "key": "RUB"
  },
  {
    "name": "加拿大元",
    "id": 13,
    "key": "CAD"
  },
  {
    "name": "新西兰币",
    "id": 14,
    "key": "NZD"
  },
  {
    "name": "瑞士法郎",
    "id": 15,
    "key": "CHF"
  },
  {
    "name": "南非兰特",
    "id": 16,
    "key": "ZAR"
  },
  {
    "name": "韩元",
    "id": 17,
    "key": "KRW"
  },
  {
    "name": "迪拉姆",
    "id": 18,
    "key": "AED"
  },
  {
    "name": "里亚尔",
    "id": 19,
    "key": "IRR"
  },
  {
    "name": "福林",
    "id": 20,
    "key": "HUF"
  },
  {
    "name": "兹罗提",
    "id": 21,
    "key": "PLN"
  },
  {
    "name": "丹麦克朗",
    "id": 22,
    "key": "DKK"
  },
  {
    "name": "瑞典克朗",
    "id": 23,
    "key": "SEK"
  },
  {
    "name": "挪威克朗",
    "id": 24,
    "key": "NOK"
  },
  {
    "name": "里拉",
    "id": 25,
    "key": "TRY"
  },
  {
    "name": "比索",
    "id": 26,
    "key": "CUP"
  },
  {
    "name": "泰铢",
    "id": 27,
    "key": "THB"
  }
]

const SALARY_UNIT_MAP = new Map()
SALARY_UNIT_DICT.forEach((item, index) => {
  SALARY_UNIT_MAP.set(item.id, item.name)
})

function formatSalary(salary: Salary) {
  const unit = SALARY_UNIT_MAP.get(salary.salaryUnit)

  if (salary.salaryTimeUnit === SALARY_CALC_UNIT.DAY) {
    return `${salary.salaryFrom.toFixed(2)} - ${salary.salaryTo.toFixed(2)} ${unit} 每日`
  } else if (salary.salaryTimeUnit === SALARY_CALC_UNIT.MONTH) {
    return `${(salary.salaryFrom / 1000).toFixed(1)}k - ${(salary.salaryTo / 1000).toFixed(1)}k ${unit} 每月`
  } else if (salary.salaryTimeUnit === SALARY_CALC_UNIT.YEAR) {
    return `${(salary.salaryFrom / 1000).toFixed(1)}k - ${(salary.salaryTo / 1000).toFixed(1)}k ${unit} 每年`
  } else {
    return ``
  }
}

export const ProjectHeader: React.FC<ProjectHeaderProps> = ({
  jobRequirement,
  positionDetail,
  customerDetail,
  className
}) => {
  const { isMobile } = useBreakpoint();

  

  // 获取用户信息
  const getUsers = (type: 'bd' | 'pm' | 'ca'): string => {
    const users: string[] = [];
    jobRequirement.properties?.forEach((item: any) => {
      if (item.key?.toLowerCase() === type) {
        users.push(item.valueName || item.value);
      }
    });
    return users.join(' / ') || '-';
  };

  // 获取优先级标签
  const getPriorityTag = (priority: number) => {
    switch (priority) {
      case 0:
        return { name: '急招', color: 'red' as const };
      case 1:
        return { name: '高优先级', color: 'orange' as const };
      case 2:
        return { name: '中优先级', color: 'blue' as const };
      default:
        return { name: '普通', color: 'gray' as const };
    }
  };

  // 构建标签数组
  const buildTags = () => {
    const tags = [];
    
    positionDetail.areaStr && tags.push({ name: positionDetail.areaStr, color: 'gray' as const });


    // HC数量
    if (positionDetail.quantityRequired) {
      tags.push({ 
        name: `${positionDetail.quantityRequired}个HC`, 
        color: 'blue' as const 
      });
    }

    // 年薪
    if (positionDetail.salaryFrom && positionDetail.salaryTo) {
      tags.push({ name: formatSalary(positionDetail), color: 'green' as const });
    }

    // 佣金
    if (positionDetail?.financeConfig?.paymentTimes > 0) {
      // > 0 按次
      tags.push({ name: '佣金', color: 'orange' as const });
    }

    return tags;
  };

  return (
    <Card className={clsx('mb-6', className)} padding="medium">
      {/* 职位标题 */}
      <div className="mb-4">
        <h1 className={clsx(
          'font-bold text-gray-900 mb-2',
          isMobile ? 'text-xl' : 'text-2xl'
        )}>
          {positionDetail.positionTitle || '职位标题'}
        </h1>

        {/* 公司和PM信息 */}
        <div className={clsx(
          'flex items-center text-gray-600 mb-3',
          isMobile ? 'text-sm flex-col items-start space-y-1' : 'text-base space-x-4'
        )}>
          <div className="flex items-center space-x-1">
            <span className="font-medium">{customerDetail.customerFullName || '公司名称'}</span>
          </div>
          {getUsers('pm') !== '-' && (
            <div className="flex items-center space-x-1">
              <User className="w-4 h-4" />
              <span>PM: {getUsers('pm')}</span>
            </div>
          )}
        </div>

        {/* 标签组 */}
        <TagGroup tags={buildTags()} className="mb-4" />
      </div>

    
    </Card>
  );
};
