/**
 * 响应式按钮组件
 * PC端：底部文档流
 * 移动端：固定定位在屏幕底部
 */

import React from 'react';
import { Button } from 'antd';
import clsx from 'clsx';
import { useBreakpoint } from '../../theme/responsive';
import { ActionButton } from '../types';

interface ActionButtonsProps {
  buttons: ActionButton[];
  className?: string;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({ 
  buttons, 
  className 
}) => {
  const { isMobile } = useBreakpoint();

  if (isMobile) {
    return (
      <>
        {/* 占位元素，防止内容被固定按钮遮挡 */}
        <div className="h-20" />
        
        {/* 固定定位的按钮组 */}
        <div className={clsx(
          'fixed bottom-0 left-0 right-0 z-50',
          'bg-white border-t border-gray-200',
          'px-4 py-3 safe-area-pb',
          className
        )}>
          <div className="flex space-x-3">
            {buttons.map((button, index) => (
              <Button
                key={index}
                type={button.type || 'default'}
                size="large"
                loading={button.loading}
                disabled={button.disabled}
                onClick={button.onClick}
                className={clsx(
                  'flex-1 h-12 font-medium',
                  button.type === 'primary' && 'bg-[#FE9111] border-[#FE9111] hover:bg-[#E8820F] hover:border-[#E8820F]'
                )}
              >
                {button.text}
              </Button>
            ))}
          </div>
        </div>
      </>
    );
  }

  // PC端：正常文档流
  return (
    <div className={clsx(
      'flex justify-center space-x-4 mt-8 pt-6 border-t border-gray-200',
      className
    )}>
      {buttons.map((button, index) => (
        <Button
          key={index}
          type={button.type || 'default'}
          size="large"
          loading={button.loading}
          disabled={button.disabled}
          onClick={button.onClick}
          className={clsx(
            'px-8 h-12 font-medium min-w-[120px]',
            button.type === 'primary' && 'bg-[#FE9111] border-[#FE9111] hover:bg-[#E8820F] hover:border-[#E8820F]'
          )}
        >
          {button.text}
        </Button>
      ))}
    </div>
  );
};

// 单个按钮组件（用于特殊场景）
interface SingleActionButtonProps extends ActionButton {
  className?: string;
  block?: boolean;
}

export const SingleActionButton: React.FC<SingleActionButtonProps> = ({
  text,
  type = 'primary',
  onClick,
  loading,
  disabled,
  className,
  block = false
}) => {
  const { isMobile } = useBreakpoint();

  return (
    <Button
      type={type}
      size={isMobile ? 'large' : 'large'}
      loading={loading}
      disabled={disabled}
      onClick={onClick}
      block={block}
      className={clsx(
        'font-medium',
        isMobile ? 'h-12' : 'h-12 px-8 min-w-[120px]',
        type === 'primary' && 'bg-[#FE9111] border-[#FE9111] hover:bg-[#E8820F] hover:border-[#E8820F]',
        className
      )}
    >
      {text}
    </Button>
  );
};
