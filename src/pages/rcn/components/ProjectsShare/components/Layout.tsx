/**
 * 项目分享页面布局组件
 * 实现响应式设计，适配PC端和移动端
 */

import React from 'react';
import clsx from 'clsx';
import { useBreakpoint } from '../../theme/responsive';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

// 主容器组件
export const Container: React.FC<LayoutProps> = ({ children, className }) => {
  const { isMobile } = useBreakpoint();
  
  return (
    <div className={clsx(
      'min-h-screen bg-gray-50',
      isMobile ? 'px-4 py-4' : 'px-6 py-6',
      className
    )}>
      <div className={clsx(
        'mx-auto',
        isMobile ? 'max-w-full' : 'max-w-4xl'
      )}>
        {children}
      </div>
    </div>
  );
};

// 卡片组件
interface CardProps extends LayoutProps {
  title?: string;
  extra?: React.ReactNode;
  padding?: 'none' | 'small' | 'medium' | 'large';
}

export const Card: React.FC<CardProps> = ({ 
  children, 
  className, 
  title, 
  extra,
  padding = 'medium' 
}) => {
  const { isMobile } = useBreakpoint();
  
  const paddingClasses = {
    none: '',
    small: isMobile ? 'p-3' : 'p-4',
    medium: isMobile ? 'p-4' : 'p-6',
    large: isMobile ? 'p-5' : 'p-8'
  };

  return (
    <div className={clsx(
      'bg-white rounded-lg shadow-sm border border-gray-200',
      paddingClasses[padding],
      className
    )}>
      {(title || extra) && (
        <div className="flex items-center justify-between mb-4">
          {title && (
            <h3 className={clsx(
              'font-semibold text-gray-900',
              isMobile ? 'text-lg' : 'text-xl'
            )}>
              {title}
            </h3>
          )}
          {extra && <div>{extra}</div>}
        </div>
      )}
      {children}
    </div>
  );
};

// 标签组件
interface TagProps {
  children: React.ReactNode;
  color?: 'blue' | 'green' | 'orange' | 'red' | 'purple' | 'gray';
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const Tag: React.FC<TagProps> = ({ 
  children, 
  color = 'blue', 
  size = 'medium',
  className 
}) => {
  const { isMobile } = useBreakpoint();
  
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-800 border-blue-200',
    green: 'bg-green-100 text-green-800 border-green-200',
    orange: 'bg-orange-100 text-orange-800 border-orange-200',
    red: 'bg-red-100 text-red-800 border-red-200',
    purple: 'bg-purple-100 text-purple-800 border-purple-200',
    gray: 'bg-gray-100 text-gray-800 border-gray-200'
  };
  
  const sizeClasses = {
    small: isMobile ? 'px-2 py-1 text-xs' : 'px-2 py-1 text-xs',
    medium: isMobile ? 'px-2.5 py-1 text-sm' : 'px-3 py-1.5 text-sm',
    large: isMobile ? 'px-3 py-1.5 text-base' : 'px-4 py-2 text-base'
  };

  return (
    <span className={clsx(
      'inline-flex items-center rounded-full border font-medium',
      colorClasses[color],
      sizeClasses[size],
      className
    )}>
      {children}
    </span>
  );
};

// 分隔线组件
interface DividerProps {
  className?: string;
  margin?: 'small' | 'medium' | 'large';
}

export const Divider: React.FC<DividerProps> = ({ 
  className, 
  margin = 'medium' 
}) => {
  const { isMobile } = useBreakpoint();
  
  const marginClasses = {
    small: isMobile ? 'my-3' : 'my-4',
    medium: isMobile ? 'my-4' : 'my-6',
    large: isMobile ? 'my-6' : 'my-8'
  };

  return (
    <hr className={clsx(
      'border-gray-200',
      marginClasses[margin],
      className
    )} />
  );
};

// 内容区域组件
interface ContentSectionProps extends LayoutProps {
  title: string;
  icon?: React.ReactNode;
}

export const ContentSection: React.FC<ContentSectionProps> = ({ 
  children, 
  title, 
  icon, 
  className 
}) => {
  const { isMobile } = useBreakpoint();

  return (
    <div className={clsx('space-y-3', className)}>
      <div className="flex items-center space-x-2">
        {icon && <div className="text-gray-600">{icon}</div>}
        <h4 className={clsx(
          'font-semibold text-gray-900',
          isMobile ? 'text-base' : 'text-lg'
        )}>
          {title}
        </h4>
      </div>
      <div className={clsx(
        'text-gray-700 leading-relaxed',
        isMobile ? 'text-sm' : 'text-base'
      )}>
        {children}
      </div>
    </div>
  );
};

// 标签组容器
interface TagGroupProps {
  tags: Array<{ name: string; color?: string }>;
  className?: string;
}

export const TagGroup: React.FC<TagGroupProps> = ({ tags, className }) => {
  const { isMobile } = useBreakpoint();
  
  const getTagColor = (index: number): TagProps['color'] => {
    const colors: TagProps['color'][] = ['blue', 'green', 'orange', 'purple', 'red'];
    return colors[index % colors.length];
  };

  return (
    <div className={clsx(
      'flex flex-wrap gap-2',
      isMobile ? 'gap-1.5' : 'gap-2',
      className
    )}>
      {tags.map((tag, index) => (
        <Tag
          key={index}
          color={getTagColor(index)}
          size={isMobile ? 'small' : 'medium'}
        >
          {tag.name}
        </Tag>
      ))}
    </div>
  );
};
