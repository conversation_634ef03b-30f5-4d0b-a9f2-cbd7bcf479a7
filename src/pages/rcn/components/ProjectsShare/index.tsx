/**
 * 项目分享页面主组件
 * 实现响应式设计，匹配截图样式
 */

import React from 'react';
import { Spin, Alert } from 'antd';
import { useParams } from 'react-router-dom';
import { Container } from './components/Layout';
import { ProjectHeader } from './components/ProjectHeader';
import { ProjectContent } from './components/ProjectContent';
import { ActionButtons } from './components/ActionButtons';
import { useProjectDetail } from './hooks/useProjectDetail';
import { ActionButton } from './types';

const ProjectsShare: React.FC = () => {
  const params = useParams<{ jobRequirementId: string }>();
  const jobRequirementId = params?.jobRequirementId || 712; // 默认值用于测试

  const { data, loading, error, refresh } = useProjectDetail({
    jobRequirementId
  });
  
  
  // 按钮配置
  const actionButtons: ActionButton[] = [
    {
      text: '查看完整信息',
      type: 'primary',
      onClick: () => {
        // 这里可以添加跳转到详情页的逻辑
        console.log('查看完整信息');
      }
    }
  ];

  // 加载状态
  if (loading) {
    return (
      <Container>
        <div className="flex justify-center items-center min-h-[400px]">
          <Spin size="large" tip="加载项目信息中..." />
        </div>
      </Container>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Container>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button
              onClick={refresh}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              重试
            </button>
          }
        />
      </Container>
    );
  }

  // 数据为空状态
  if (!data) {
    return (
      <Container>
        <Alert
          message="暂无数据"
          description="未找到相关项目信息"
          type="info"
          showIcon
        />
      </Container>
    );
  }

  console.log(`data: `, data)

  return (
    <Container>
      {/* 项目头部信息 */}
      <ProjectHeader
        jobRequirement={data.jobRequirement}
        positionDetail={data.positionDetail}
        customerDetail={data.customerDetail}
      />

      {/* 项目内容区域 */}
      <ProjectContent
        positionDetail={data.positionDetail}
        customerDetail={data.customerDetail}
        financeConfig={data.financeConfig}
      />

      {/* 响应式按钮组 */}
      <ActionButtons buttons={actionButtons} />
    </Container>
  );
};

export default ProjectsShare;
